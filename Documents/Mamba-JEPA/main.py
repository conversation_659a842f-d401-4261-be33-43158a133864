# =================================================================================
# Production-Grade Mamba-JEPA with MLLM Knowledge Distillation (FIXED v3)
# =================================================================================
#
# This script implements the Mamba-JEPA architecture using a working Multimodal LLM
# as the "teacher" model for knowledge distillation.
#
# Fixed Issues:
# - Dynamic dimension handling for teacher features
# - Removed 4-bit quantization to avoid memory issues
# - Better error handling and fallback mechanisms
# - Proper dtype consistency throughout the pipeline
#
# =================================================================================

# -- 1. SETUP AND INSTALLATIONS --
import subprocess
import sys
import os

# Skip package installation since we're using virtual environment
print("Using pre-configured virtual environment...")

# Import libraries with error handling
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import DataLoader, Dataset
    import torchvision
    import torchvision.transforms as transforms
    from tqdm.auto import tqdm
    from einops import rearrange
    import numpy as np
    import matplotlib.pyplot as plt
    from PIL import Image
    import warnings
    warnings.filterwarnings("ignore")
    print("✓ Basic imports successful")

    # Try to import transformers components with individual error handling
    transformers_available = True
    blip2_available = False
    vit_available = False
    mamba_available = False

    try:
        from transformers import AutoModel, AutoConfig
        print("✓ Basic transformers imported")
    except Exception as e:
        print(f"✗ Basic transformers import failed: {e}")
        transformers_available = False

    if transformers_available:
        try:
            from transformers import Blip2Processor, Blip2ForConditionalGeneration
            blip2_available = True
            print("✓ BLIP-2 imports successful")
        except Exception as e:
            print(f"✗ BLIP-2 import failed (will use fallback): {e}")

        try:
            from transformers import ViTModel, ViTImageProcessor
            vit_available = True
            print("✓ ViT imports successful")
        except Exception as e:
            print(f"✗ ViT import failed (will use fallback): {e}")

    print("✓ All available imports loaded")
except ImportError as e:
    print(f"✗ Critical import error: {e}")
    transformers_available = False
    blip2_available = False
    vit_available = False
    mamba_available = False

# -- 2. PRODUCTION CONFIGURATION --
class Config:
    # Model Hub Names
    TEACHER_MODEL_NAME = "Salesforce/blip2-opt-2.7b"
    STUDENT_MODEL_NAME = "state-spaces/mamba-130m"

    # Data Parameters
    IMAGE_SIZE = 224
    PATCH_SIZE = 16
    DATASET_SIZE = 5000  # Increased for production

    # Training Parameters - Production Settings
    BATCH_SIZE = 8  # Increased batch size
    NUM_EPOCHS = 20  # More epochs for convergence
    LEARNING_RATE = 5e-5  # Lower learning rate for stability
    WARMUP_STEPS = 500
    WEIGHT_DECAY = 0.01

    # JEPA Parameters
    CONTEXT_RATIO = 0.6

    # Device settings
    DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
    USE_4BIT = False

    # Production Features
    SAVE_CHECKPOINTS = True
    CHECKPOINT_DIR = "./checkpoints"
    LOG_INTERVAL = 50
    EVAL_INTERVAL = 200

config = Config()
print(f"Using device: {config.DEVICE}")
print(f"4-bit quantization: {config.USE_4BIT}")

# -- 3. CUSTOM DATASET CLASS --
class SimpleCIFAR10Dataset(Dataset):
    def __init__(self, num_samples=100):
        """Create a simple CIFAR-10 dataset using torchvision"""
        self.transform = transforms.Compose([
            transforms.Resize((config.IMAGE_SIZE, config.IMAGE_SIZE)),
            transforms.ToTensor(),
        ])
        
        # Try to load CIFAR-10, fallback to synthetic data if it fails
        try:
            self.dataset = torchvision.datasets.CIFAR10(
                root='./data', 
                train=True, 
                download=True, 
                transform=None  # We'll apply transform manually
            )
            self.use_real_data = True
            print(f"✓ CIFAR-10 loaded successfully")
        except Exception as e:
            print(f"✗ CIFAR-10 loading failed: {e}")
            print("Creating synthetic dataset...")
            self.use_real_data = False
            
        self.num_samples = min(num_samples, len(self.dataset) if self.use_real_data else num_samples)
    
    def __len__(self):
        return self.num_samples
    
    def __getitem__(self, idx):
        if self.use_real_data:
            image, label = self.dataset[idx]
        else:
            # Create synthetic image if real data fails
            image = Image.fromarray(np.random.randint(0, 255, (32, 32, 3), dtype=np.uint8))
            label = np.random.randint(0, 10)
        
        return {
            'image': image,
            'label': label
        }

# -- 4. DATA LOADING --
def collate_fn(batch):
    """Custom collate function"""
    images = []
    labels = []
    
    for item in batch:
        img = item['image']
        if not isinstance(img, Image.Image):
            img = Image.fromarray(np.array(img))
        if img.mode != 'RGB':
            img = img.convert('RGB')
        images.append(img)
        labels.append(item['label'])
    
    return {
        'images': images,
        'labels': labels
    }

# Create dataset and dataloader - Production Scale
print("Creating production dataset...")
try:
    dataset = SimpleCIFAR10Dataset(num_samples=config.DATASET_SIZE)  # Production dataset size
    dataloader = DataLoader(dataset, batch_size=config.BATCH_SIZE, collate_fn=collate_fn, shuffle=True, num_workers=4, pin_memory=True)
    print(f"✓ Production dataset created with {len(dataset)} samples")
except Exception as e:
    print(f"✗ Dataset creation failed: {e}")
    # Create a minimal fallback dataset
    class FallbackDataset(Dataset):
        def __len__(self):
            return 1000  # Larger fallback for production
        def __getitem__(self, idx):
            # Create random RGB image
            img_array = np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8)
            return {'image': Image.fromarray(img_array), 'label': idx % 10}

    dataset = FallbackDataset()
    dataloader = DataLoader(dataset, batch_size=config.BATCH_SIZE, collate_fn=collate_fn, num_workers=4, pin_memory=True)
    print("✓ Using fallback synthetic dataset")

# -- 5. MODEL LOADING WITH BETTER DIMENSION HANDLING --

# Teacher Model Loading
teacher_model = None
teacher_processor = None
teacher_dim = 768  # Will be dynamically determined

print("Loading teacher model...")
# Try BLIP-2 first (only if imports were successful)
if blip2_available:
    try:
        teacher_processor = Blip2Processor.from_pretrained(config.TEACHER_MODEL_NAME)
        teacher_model = Blip2ForConditionalGeneration.from_pretrained(
            config.TEACHER_MODEL_NAME,
            torch_dtype=torch.float32  # Use float32 to avoid issues
        ).to(config.DEVICE)

        for param in teacher_model.parameters():
            param.requires_grad = False
        teacher_model.eval()
        print("✓ BLIP-2 teacher model loaded")
        model_type = "blip2"

        # Test to get actual dimension
        with torch.no_grad():
            dummy_img = Image.fromarray(np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8))
            inputs = teacher_processor(images=[dummy_img], return_tensors="pt").to(config.DEVICE)
            vision_outputs = teacher_model.vision_model(**inputs)
            teacher_dim = vision_outputs.last_hidden_state.shape[-1]
            print(f"✓ Detected teacher dimension: {teacher_dim}")

    except Exception as e:
        print(f"BLIP-2 loading failed: {e}")
        blip2_available = False

# Fallback to ViT if BLIP-2 failed or wasn't available
if not blip2_available and vit_available:
    try:
        teacher_processor = ViTImageProcessor.from_pretrained('google/vit-base-patch16-224')
        teacher_model = ViTModel.from_pretrained('google/vit-base-patch16-224').to(config.DEVICE)
        for param in teacher_model.parameters():
            param.requires_grad = False
        teacher_model.eval()
        teacher_dim = 768
        print("✓ ViT fallback teacher model loaded")
        model_type = "vit"
    except Exception as e2:
        print(f"ViT loading also failed: {e2}")
        vit_available = False

# Ultimate fallback: simple CNN if both BLIP-2 and ViT failed
if not blip2_available and not vit_available:
    print("Creating simple CNN teacher model...")
    # Ultimate fallback: simple CNN
    class SimpleCNN(nn.Module):
        def __init__(self):
            super().__init__()
            self.features = nn.Sequential(
                nn.Conv2d(3, 64, 3, padding=1),
                nn.ReLU(),
                nn.AdaptiveAvgPool2d((7, 7)),
                nn.Flatten(),
                nn.Linear(64 * 7 * 7, 768)
            )
        def forward(self, x):
            return self.features(x)

    teacher_model = SimpleCNN().to(config.DEVICE)
    for param in teacher_model.parameters():
        param.requires_grad = False
    teacher_model.eval()
    teacher_processor = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    teacher_dim = 768
    print("✓ Simple CNN teacher model created")
    model_type = "cnn"

# Student Model Loading
print("Loading student model...")
if transformers_available:
    try:
        student_config = AutoConfig.from_pretrained(config.STUDENT_MODEL_NAME)
        student_model = AutoModel.from_pretrained(config.STUDENT_MODEL_NAME).to(config.DEVICE).float()
        mamba_available = True
        print("✓ Mamba student model loaded")
    except Exception as e:
        print(f"Mamba loading failed: {e}")
        mamba_available = False

if not mamba_available:
    # Fallback: simple transformer
    print("Creating simple transformer student model...")
    class SimpleTransformer(nn.Module):
        def __init__(self, d_model=768):
            super().__init__()
            self.d_model = d_model
            self.transformer = nn.TransformerEncoder(
                nn.TransformerEncoderLayer(d_model=d_model, nhead=8, batch_first=True),
                num_layers=3
            )

        def forward(self, inputs_embeds):
            return type('Output', (), {'last_hidden_state': self.transformer(inputs_embeds)})()

    student_model = SimpleTransformer().to(config.DEVICE).float()
    student_config = type('Config', (), {'hidden_size': 768})()
    print("✓ Simple transformer student model created")

# -- 6. HELPER MODULES WITH DYNAMIC DIMENSIONS --
D_STUDENT = getattr(student_config, 'hidden_size', 768)
D_TEACHER = teacher_dim  # Use detected dimension

PATCH_DIM = 3 * config.PATCH_SIZE * config.PATCH_SIZE

print(f"Teacher dim: {D_TEACHER}, Student dim: {D_STUDENT}")

# Create modules with correct dimensions
patch_projector = nn.Linear(PATCH_DIM, D_STUDENT).to(config.DEVICE).float()
repr_projector = nn.Linear(D_TEACHER, D_STUDENT).to(config.DEVICE).float()
predictor = nn.Sequential(
    nn.Linear(D_STUDENT, D_STUDENT * 2),
    nn.GELU(),
    nn.Linear(D_STUDENT * 2, D_STUDENT)
).to(config.DEVICE).float()

# -- 7. PRODUCTION TRAINING SETUP --
params_to_train = (
    list(student_model.parameters()) +
    list(patch_projector.parameters()) +
    list(repr_projector.parameters()) +
    list(predictor.parameters())
)

# Production optimizer with weight decay and scheduling
optimizer = optim.AdamW(params_to_train, lr=config.LEARNING_RATE, weight_decay=config.WEIGHT_DECAY)
scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=config.NUM_EPOCHS)
loss_fn = nn.MSELoss()

# Create checkpoint directory
if config.SAVE_CHECKPOINTS:
    os.makedirs(config.CHECKPOINT_DIR, exist_ok=True)
    print(f"✓ Checkpoint directory created: {config.CHECKPOINT_DIR}")

# Training metrics tracking
training_metrics = {
    'losses': [],
    'learning_rates': [],
    'epoch_times': []
}

# -- 8. IMPROVED TRAINING FUNCTIONS --
def extract_teacher_features(images):
    """Extract features from teacher model with proper dimension handling"""
    with torch.no_grad():
        if model_type == "blip2":
            inputs = teacher_processor(images=images, return_tensors="pt").to(config.DEVICE)
            vision_outputs = teacher_model.vision_model(**inputs)
            # Use mean pooling over spatial dimensions
            features = vision_outputs.last_hidden_state.mean(dim=1)
            return features.float()
        elif model_type == "vit":
            inputs = teacher_processor(images=images, return_tensors="pt").to(config.DEVICE)
            outputs = teacher_model(**inputs)
            features = outputs.last_hidden_state[:, 0]  # CLS token
            return features.float()
        else:  # CNN fallback
            # Convert PIL to tensor
            tensors = []
            for img in images:
                tensor = teacher_processor(img)
                tensors.append(tensor)
            batch_tensor = torch.stack(tensors).to(config.DEVICE)
            features = teacher_model(batch_tensor)
            return features.float()

def create_patches(images):
    """Convert images to patches"""
    image_tensors = []
    for img in images:
        img = img.resize((config.IMAGE_SIZE, config.IMAGE_SIZE))
        img_tensor = torch.tensor(np.array(img), dtype=torch.float32) / 255.0
        img_tensor = img_tensor.permute(2, 0, 1)
        image_tensors.append(img_tensor)
    
    batch_tensor = torch.stack(image_tensors).to(config.DEVICE)
    patches = rearrange(
        batch_tensor, 'b c (h p1) (w p2) -> b (h w) (p1 p2 c)',
        p1=config.PATCH_SIZE, p2=config.PATCH_SIZE
    )
    return patches

# -- 9. PRODUCTION TRAINING LOOP --
print("\n🚀 Starting Production Training...")
print(f"Patch dimension: {PATCH_DIM}")
print(f"Number of patches per image: {(config.IMAGE_SIZE // config.PATCH_SIZE) ** 2}")
print(f"Dataset size: {len(dataset)}")
print(f"Batch size: {config.BATCH_SIZE}")
print(f"Total epochs: {config.NUM_EPOCHS}")
print(f"Device: {config.DEVICE}")

import time

student_model.train()
predictor.train()
patch_projector.train()
repr_projector.train()

best_loss = float('inf')
global_step = 0

for epoch in range(config.NUM_EPOCHS):
    epoch_start_time = time.time()
    pbar = tqdm(dataloader, desc=f"Epoch {epoch+1}/{config.NUM_EPOCHS}")
    epoch_loss = 0.0
    batch_count = 0

    for batch in pbar:
        try:
            optimizer.zero_grad()

            images = batch['images']

            # Create patches
            patches = create_patches(images)
            num_patches = patches.shape[1]
            context_len = int(num_patches * config.CONTEXT_RATIO)
            context_patches = patches[:, :context_len, :].float()

            # Student forward pass
            context_embeddings = patch_projector(context_patches)
            student_output = student_model(inputs_embeds=context_embeddings).last_hidden_state
            context_representation = student_output.mean(dim=1).float()

            # Predict target
            predicted_target = predictor(context_representation)

            # Get teacher features
            teacher_features = extract_teacher_features(images)
            true_target = repr_projector(teacher_features)

            # Loss
            loss = loss_fn(predicted_target, true_target)

            # Backward
            loss.backward()
            torch.nn.utils.clip_grad_norm_(params_to_train, max_norm=1.0)
            optimizer.step()

            epoch_loss += loss.item()
            batch_count += 1
            global_step += 1

            # Update progress bar
            current_lr = optimizer.param_groups[0]['lr']
            pbar.set_postfix({
                'loss': f'{loss.item():.6f}',
                'lr': f'{current_lr:.2e}',
                'step': global_step
            })

            # Log at intervals
            if global_step % config.LOG_INTERVAL == 0:
                print(f"\nStep {global_step}: Loss = {loss.item():.6f}, LR = {current_lr:.2e}")

        except Exception as e:
            print(f"Batch error: {e}")
            if batch_count == 0:  # Print debug info for first batch error
                import traceback
                traceback.print_exc()
            continue

    # End of epoch processing
    if batch_count > 0:
        avg_loss = epoch_loss / batch_count
        training_metrics['losses'].append(avg_loss)
        training_metrics['learning_rates'].append(optimizer.param_groups[0]['lr'])

        epoch_time = time.time() - epoch_start_time
        training_metrics['epoch_times'].append(epoch_time)

        print(f"\n📊 Epoch {epoch+1} Summary:")
        print(f"   Average Loss: {avg_loss:.6f}")
        print(f"   Learning Rate: {optimizer.param_groups[0]['lr']:.2e}")
        print(f"   Epoch Time: {epoch_time:.1f}s")
        print(f"   Batches Processed: {batch_count}")

        # Save checkpoint if best model
        if config.SAVE_CHECKPOINTS and avg_loss < best_loss:
            best_loss = avg_loss
            checkpoint = {
                'epoch': epoch + 1,
                'student_model_state_dict': student_model.state_dict(),
                'patch_projector_state_dict': patch_projector.state_dict(),
                'repr_projector_state_dict': repr_projector.state_dict(),
                'predictor_state_dict': predictor.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'loss': avg_loss,
                'config': config.__dict__
            }
            checkpoint_path = os.path.join(config.CHECKPOINT_DIR, f'best_model_epoch_{epoch+1}.pt')
            torch.save(checkpoint, checkpoint_path)
            print(f"   💾 Best model saved: {checkpoint_path}")

        # Step scheduler
        scheduler.step()

    else:
        print(f"Epoch {epoch+1}: No successful batches")

# -- 10. PRODUCTION RESULTS AND ANALYSIS --
if training_metrics['losses']:
    # Create comprehensive training plots
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

    # Loss curve
    ax1.plot(training_metrics['losses'], 'b-', linewidth=2, marker='o')
    ax1.set_title("Training Loss", fontsize=14)
    ax1.set_xlabel("Epoch")
    ax1.set_ylabel("MSE Loss")
    ax1.grid(True, alpha=0.3)

    # Learning rate schedule
    ax2.plot(training_metrics['learning_rates'], 'r-', linewidth=2, marker='s')
    ax2.set_title("Learning Rate Schedule", fontsize=14)
    ax2.set_xlabel("Epoch")
    ax2.set_ylabel("Learning Rate")
    ax2.set_yscale('log')
    ax2.grid(True, alpha=0.3)

    # Epoch times
    ax3.bar(range(len(training_metrics['epoch_times'])), training_metrics['epoch_times'])
    ax3.set_title("Training Time per Epoch", fontsize=14)
    ax3.set_xlabel("Epoch")
    ax3.set_ylabel("Time (seconds)")
    ax3.grid(True, alpha=0.3)

    # Loss improvement
    if len(training_metrics['losses']) > 1:
        improvements = [(training_metrics['losses'][0] - loss) / training_metrics['losses'][0] * 100
                       for loss in training_metrics['losses']]
        ax4.plot(improvements, 'g-', linewidth=2, marker='^')
        ax4.set_title("Cumulative Improvement", fontsize=14)
        ax4.set_xlabel("Epoch")
        ax4.set_ylabel("Improvement (%)")
        ax4.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(config.CHECKPOINT_DIR, 'training_analysis.png'), dpi=300, bbox_inches='tight')
    plt.show()

    # Print comprehensive results
    print(f"\n🎉 PRODUCTION TRAINING COMPLETED!")
    print(f"=" * 50)
    print(f"📊 Training Statistics:")
    print(f"   • Total Epochs: {len(training_metrics['losses'])}")
    print(f"   • Final Loss: {training_metrics['losses'][-1]:.6f}")
    print(f"   • Best Loss: {min(training_metrics['losses']):.6f}")

    if len(training_metrics['losses']) > 1:
        improvement = ((training_metrics['losses'][0] - training_metrics['losses'][-1]) / training_metrics['losses'][0] * 100)
        print(f"   • Total Improvement: {improvement:.1f}%")

    total_time = sum(training_metrics['epoch_times'])
    avg_time = total_time / len(training_metrics['epoch_times'])
    print(f"   • Total Training Time: {total_time:.1f}s ({total_time/60:.1f}m)")
    print(f"   • Average Epoch Time: {avg_time:.1f}s")

    print(f"\n💾 Model artifacts saved in: {config.CHECKPOINT_DIR}")
    print(f"   • Best model checkpoint")
    print(f"   • Training analysis plots")

else:
    print("❌ Training failed - no metrics recorded")

print(f"\n--- 🚀 Mamba-JEPA Production Training Complete ---")